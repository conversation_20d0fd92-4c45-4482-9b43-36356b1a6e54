#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# API配置
API_CONFIG = {
    # API基础URL - 聚水潭API域名
    'BASE_URL': 'https://goods.scm121.com',

    # 授权token - 请替换为实际的JWT token
    'AUTHORIZATION': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiIxMzA5Nzc5OTMxNyIsImN1cnJlbnRfdXNlcl9vcmdfaWQiOm51bGwsImNvSWQiOiIxNDg0OTExOCIsImF1dGhvcml0aWVzIjpbIkpTVC1jaGFubmVsIiwibXVsdGlMb2dpbiIsIkpTVC1zdXBwbGllciJdLCJjdXJyZW50X3VzZXJfb3JnX2NvZGUiOm51bGwsImNsaWVudF9pZCI6InBjIiwidWlkIjoiMjA3NTgxMDkiLCJyb2xlSWRzIjpbXSwic2NvcGUiOlsidHJ1c3QiXSwiZXhwaXJhdGlvbiI6MTc1NDkwMjgxODcwNiwiZXhwIjoxNzU0OTAyODE4LCJ1c2VyIjp7ImNvSWQiOiIxNDg0OTExOCIsInVpZCI6IjIwNzU4MTA5IiwibG9naW5OYW1lIjoiMTMwOTc3OTkzMTciLCJuaWNrTmFtZSI6ImFkbWluIiwiY29OYW1lIjoi6YCU6K6v56eR5oqAIiwicHJveHlMb2dpbk5hbWUiOm51bGwsImxvZ2luV2F5IjoiVU5JT04iLCJyb2xlSWRzIjoiMTEsMTIsNDEsMjcsMTMsMzksMjMsMzQsMTQsNDAifSwianRpIjoiYTJkMTgwNjMtY2IxMy00NjQ1LWI0ZmEtNTg5NTIzODFjMjJlIn0.DUIBbqtkYS2kblk-UBy22-IvlEhDg39lEFUgaMEB2vo',

    # 店铺ID - 请替换为实际的店铺ID
    'SHOP_ID': '18599275',

    # 请求间隔（秒）- 避免请求过于频繁
    'REQUEST_INTERVAL': 0.5,

    # 平台间请求间隔（秒）
    'PLATFORM_INTERVAL': 2.0
}

# 平台配置
PLATFORM_CONFIG = {
    'TouTiaoFXG': {
        'name': '抖店',
        'code': 'TouTiaoFXG',
        'description': '抖音电商平台（聚水潭）',
        'need_shop_id': False
    },
    'Xiaohs': {
        'name': '小红书',
        'code': 'Xiaohs',
        'description': '小红书电商平台（聚水潭）',
        'need_shop_id': True
    },
    'KWaiShop': {
        'name': '快手',
        'code': 'KWaiShop',
        'description': '快手电商平台（聚水潭）',
        'need_shop_id': False
    }
}

# 输出配置
OUTPUT_CONFIG = {
    # 数据保存目录
    'DATA_DIR': 'data',
    
    # 日志文件名
    'LOG_FILE': 'category_fetch.log',
    
    # 是否保存详细的调试信息
    'DEBUG_MODE': False
}
