#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# API配置
API_CONFIG = {
    # API基础URL - 聚水潭API域名
    'BASE_URL': 'https://goods.scm121.com',

    # 授权token - 请替换为实际的JWT token
    'AUTHORIZATION': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-actual-token',

    # 店铺ID - 请替换为实际的店铺ID
    'SHOP_ID': '18599275',

    # 请求间隔（秒）- 避免请求过于频繁
    'REQUEST_INTERVAL': 0.5,

    # 平台间请求间隔（秒）
    'PLATFORM_INTERVAL': 2.0
}

# 平台配置
PLATFORM_CONFIG = {
    'Fxg': {
        'name': '抖店',
        'code': 'Fxg',
        'description': '抖音电商平台（聚水潭）'
    },
    'Xiaohs': {
        'name': '小红书',
        'code': 'Xiaohs',
        'description': '小红书电商平台（聚水潭）'
    },
    'Kwaishop': {
        'name': '快手',
        'code': 'Kwaishop',
        'description': '快手电商平台（聚水潭）'
    }
}

# 输出配置
OUTPUT_CONFIG = {
    # 数据保存目录
    'DATA_DIR': 'data',
    
    # 日志文件名
    'LOG_FILE': 'category_fetch.log',
    
    # 是否保存详细的调试信息
    'DEBUG_MODE': False
}
