#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# API配置
API_CONFIG = {
    # API基础URL - 请替换为实际的API域名
    'BASE_URL': 'https://your-api-domain.com',
    
    # 授权token - 请替换为实际的授权token
    'AUTHORIZATION': 'your-auth-token',
    
    # 请求间隔（秒）- 避免请求过于频繁
    'REQUEST_INTERVAL': 0.5,
    
    # 平台间请求间隔（秒）
    'PLATFORM_INTERVAL': 2.0
}

# 平台配置
PLATFORM_CONFIG = {
    'FXG': {
        'name': '抖店',
        'code': 'FXG',
        'description': '抖音电商平台（聚水潭）'
    },
    'XHS': {
        'name': '小红书',
        'code': 'XHS', 
        'description': '小红书电商平台（聚水潭）'
    },
    'KWAISHOP': {
        'name': '快手',
        'code': 'KWAISHOP',
        'description': '快手电商平台（聚水潭）'
    }
}

# 输出配置
OUTPUT_CONFIG = {
    # 数据保存目录
    'DATA_DIR': 'data',
    
    # 日志文件名
    'LOG_FILE': 'category_fetch.log',
    
    # 是否保存详细的调试信息
    'DEBUG_MODE': False
}
