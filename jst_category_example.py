#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聚水潭类目获取示例脚本
基于真实的聚水潭API接口
"""

from category_fetcher import CategoryFetcher
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_xiaohs_categories():
    """示例：获取小红书类目信息"""

    # 聚水潭API配置
    BASE_URL = "https://goods.scm121.com"

    # 请替换为您的实际JWT token
    AUTHORIZATION = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiIxMzA5Nzc5OTMxNyIsImN1cnJlbnRfdXNlcl9vcmdfaWQiOm51bGwsImNvSWQiOiIxNDg0OTExOCIsImF1dGhvcml0aWVzIjpbIkpTVC1jaGFubmVsIiwibXVsdGlMb2dpbiIsIkpTVC1zdXBwbGllciJdLCJjdXJyZW50X3VzZXJfb3JnX2NvZGUiOm51bGwsImNsaWVudF9pZCI6InBjIiwidWlkIjoiMjA3NTgxMDkiLCJyb2xlSWRzIjpbXSwic2NvcGUiOlsidHJ1c3QiXSwiZXhwaXJhdGlvbiI6MTc1NDkwMjgxODcwNiwiZXhwIjoxNzU0OTAyODE4LCJ1c2VyIjp7ImNvSWQiOiIxNDg0OTExOCIsInVpZCI6IjIwNzU4MTA5IiwibG9naW5OYW1lIjoiMTMwOTc3OTkzMTciLCJuaWNrTmFtZSI6ImFkbWluIiwiY29OYW1lIjoi6YCU6K6v56eR5oqAIiwicHJveHlMb2dpbk5hbWUiOm51bGwsImxvZ2luV2F5IjoiVU5JT04iLCJyb2xlSWRzIjoiMTEsMTIsNDEsMjcsMTMsMzksMjMsMzQsMTQsNDAifSwianRpIjoiYTJkMTgwNjMtY2IxMy00NjQ1LWI0ZmEtNTg5NTIzODFjMjJlIn0.DUIBbqtkYS2kblk-UBy22-IvlEhDg39lEFUgaMEB2vo"

    # 请替换为您的实际店铺ID（小红书需要）
    SHOP_ID = "18599275"

    # 创建获取器
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)

    # 获取小红书类目
    logger.info("开始获取小红书类目信息...")
    categories = fetcher.fetch_categories_recursive('Xiaohs')

    if categories:
        fetcher.save_categories('Xiaohs', categories)
        logger.info("小红书类目信息获取完成")
    else:
        logger.error("未获取到小红书类目信息")

def example_douyin_categories():
    """示例：获取抖店类目信息"""

    BASE_URL = "https://goods.scm121.com"
    AUTHORIZATION = "your-jwt-token"  # 请替换
    SHOP_ID = ""  # 抖店不需要shopId

    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)

    # 获取抖店类目
    logger.info("开始获取抖店类目信息...")
    categories = fetcher.fetch_categories_recursive('TouTiaoFXG')

    if categories:
        fetcher.save_categories('TouTiaoFXG', categories)
        logger.info("抖店类目信息获取完成")
    else:
        logger.error("未获取到抖店类目信息")

def example_kuaishou_categories():
    """示例：获取快手类目信息"""

    BASE_URL = "https://goods.scm121.com"
    AUTHORIZATION = "your-jwt-token"  # 请替换
    SHOP_ID = ""  # 快手不需要shopId

    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)

    # 获取快手类目
    logger.info("开始获取快手类目信息...")
    categories = fetcher.fetch_categories_recursive('KWaiShop')

    if categories:
        fetcher.save_categories('KWaiShop', categories)
        logger.info("快手类目信息获取完成")
    else:
        logger.error("未获取到快手类目信息")

def example_single_request():
    """示例：单次请求获取顶级类目"""
    
    BASE_URL = "https://goods.scm121.com"
    AUTHORIZATION = "your-jwt-token"  # 请替换
    SHOP_ID = "your-shop-id"  # 请替换
    
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)
    
    # 只获取小红书顶级类目
    logger.info("获取小红书顶级类目...")
    top_categories = fetcher.get_categories_by_pid('Xiaohs', '0')
    
    logger.info(f"获取到{len(top_categories)}个顶级类目:")
    for cat in top_categories:
        logger.info(f"- {cat.categoryName} (ID: {cat.categoryId})")

def example_specific_category():
    """示例：获取特定类目的子类目"""
    
    BASE_URL = "https://goods.scm121.com"
    AUTHORIZATION = "your-jwt-token"  # 请替换
    SHOP_ID = "your-shop-id"  # 请替换
    
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)
    
    # 先获取顶级类目
    top_categories = fetcher.get_categories_by_pid('Xiaohs', '0')
    
    if top_categories:
        # 获取第一个顶级类目的子类目
        first_category = top_categories[0]
        logger.info(f"获取类目 '{first_category.categoryName}' 的子类目...")
        
        sub_categories = fetcher.get_categories_by_pid('Xiaohs', first_category.categoryId)
        
        logger.info(f"获取到{len(sub_categories)}个子类目:")
        for cat in sub_categories:
            logger.info(f"- {cat.categoryName} (ID: {cat.categoryId})")

def example_all_platforms():
    """示例：获取所有平台的类目信息"""

    BASE_URL = "https://goods.scm121.com"
    AUTHORIZATION = "your-jwt-token"  # 请替换
    SHOP_ID = "your-shop-id"  # 小红书需要，其他平台可以为空

    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)

    # 获取所有支持的平台
    platforms = ['TouTiaoFXG', 'Xiaohs', 'KWaiShop']

    for platform in platforms:
        platform_name = fetcher.PLATFORMS.get(platform, platform)
        logger.info(f"开始获取{platform_name}类目信息...")

        try:
            categories = fetcher.fetch_categories_recursive(platform)
            if categories:
                fetcher.save_categories(platform, categories)
                logger.info(f"{platform_name}类目信息获取完成")
            else:
                logger.warning(f"{platform_name}未获取到类目信息")
        except Exception as e:
            logger.error(f"获取{platform_name}类目信息失败: {e}")

def test_api_connection():
    """测试API连接"""
    
    BASE_URL = "https://goods.scm121.com"
    AUTHORIZATION = "your-jwt-token"  # 请替换
    SHOP_ID = "your-shop-id"  # 请替换
    
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)
    
    logger.info("测试API连接...")
    
    try:
        # 尝试获取小红书顶级类目
        categories = fetcher.get_categories_by_pid('Xiaohs', '0')
        
        if categories:
            logger.info(f"✅ API连接成功！获取到{len(categories)}个类目")
            logger.info("前3个类目:")
            for i, cat in enumerate(categories[:3]):
                logger.info(f"{i+1}. {cat.categoryName} (ID: {cat.categoryId})")
        else:
            logger.warning("⚠️ API连接成功，但未获取到数据")
            
    except Exception as e:
        logger.error(f"❌ API连接失败: {e}")

def main():
    """主函数"""
    print("聚水潭类目获取示例")
    print("=" * 50)
    print("请选择要运行的示例:")
    print("1. 测试API连接")
    print("2. 获取小红书完整类目树")
    print("3. 获取抖店完整类目树")
    print("4. 获取快手完整类目树")
    print("5. 获取顶级类目")
    print("6. 获取特定类目的子类目")
    print("7. 获取所有平台类目")
    print("0. 退出")

    while True:
        choice = input("\n请输入选择 (0-7): ").strip()

        if choice == "0":
            print("退出程序")
            break
        elif choice == "1":
            test_api_connection()
        elif choice == "2":
            example_xiaohs_categories()
        elif choice == "3":
            example_douyin_categories()
        elif choice == "4":
            example_kuaishou_categories()
        elif choice == "5":
            example_single_request()
        elif choice == "6":
            example_specific_category()
        elif choice == "7":
            example_all_platforms()
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
