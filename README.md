# 铺货助手平台类目信息获取工具

这是一个用于获取铺货助手平台类目信息的Python脚本，支持递归获取抖店、小红书、快手三个平台的完整类目树结构。

## 功能特性

- 🔄 **递归获取**: 自动递归获取完整的类目树结构
- 🏪 **多平台支持**: 支持抖店(FXG)、小红书(XHS)、快手(KWAISHOP)三个平台
- 💾 **数据保存**: 自动保存类目数据为JSON格式
- 📊 **统计信息**: 提供类目数量统计
- 🛡️ **错误处理**: 完善的错误处理和重试机制
- 📝 **日志记录**: 详细的操作日志记录

## 文件结构

```
├── category_fetcher.py    # 主程序文件
├── config.py             # 配置文件
├── README.md             # 使用说明
├── requirements.txt      # 依赖包列表
├── data/                 # 数据输出目录
│   ├── 抖店_categories.json
│   ├── 小红书_categories.json
│   └── 快手_categories.json
└── category_fetch.log    # 日志文件
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

在使用前，请先修改 `config.py` 文件中的配置：

```python
API_CONFIG = {
    'BASE_URL': 'https://your-api-domain.com',  # 替换为实际的API域名
    'AUTHORIZATION': 'your-auth-token',         # 替换为实际的授权token
    'REQUEST_INTERVAL': 0.5,                    # 请求间隔
    'PLATFORM_INTERVAL': 2.0                   # 平台间请求间隔
}
```

## 使用方法

### 方法一：直接运行主程序

```bash
python category_fetcher.py
```

### 方法二：使用配置文件

修改 `category_fetcher.py` 中的配置导入：

```python
from config import API_CONFIG

def main():
    fetcher = CategoryFetcher(
        API_CONFIG['BASE_URL'], 
        API_CONFIG['AUTHORIZATION']
    )
    fetcher.fetch_all_platforms()
```

### 方法三：单独获取某个平台

```python
from category_fetcher import CategoryFetcher

# 创建获取器
fetcher = CategoryFetcher("https://api-domain.com", "your-token")

# 只获取抖店类目
categories = fetcher.fetch_categories_recursive('FXG')
fetcher.save_categories('FXG', categories)
```

## API接口说明

脚本使用以下API接口：

### 1. 获取顶级类目列表
```
GET /v1/enterprise/product/supply-category/top/list/{platform}
```

### 2. 获取类目树信息
```
GET /v1/enterprise/product/supply-category/tree/{platform}/{parentId}
```

### 平台代码说明
- `FXG`: 抖店（聚水潭）
- `XHS`: 小红书（聚水潭）
- `KWAISHOP`: 快手（聚水潭）

## 输出数据格式

生成的JSON文件结构如下：

```json
{
  "platform": "FXG",
  "platform_name": "抖店",
  "total_count": 1250,
  "categories": [
    {
      "id": "123456",
      "platform": "FXG",
      "name": "服装内衣",
      "imageUrl": "https://example.com/image.jpg",
      "parentId": "",
      "level": 1,
      "leaf": 0,
      "showStatus": 1,
      "children": [
        {
          "id": "123457",
          "platform": "FXG",
          "name": "女装",
          "parentId": "123456",
          "level": 2,
          "leaf": 0,
          "children": [...]
        }
      ]
    }
  ]
}
```

### 字段说明
- `id`: 类目ID
- `platform`: 平台代码
- `name`: 类目名称
- `imageUrl`: 类目图片URL
- `parentId`: 父类目ID
- `level`: 类目层级
- `leaf`: 是否叶子节点 (0:否, 1:是)
- `showStatus`: 显示状态
- `children`: 子类目列表

## 注意事项

1. **API限制**: 请注意API的调用频率限制，脚本已内置请求间隔控制
2. **授权token**: 确保使用有效的授权token
3. **网络稳定**: 建议在网络稳定的环境下运行
4. **数据量**: 完整的类目树可能包含大量数据，请确保有足够的存储空间
5. **错误处理**: 如遇到网络错误，脚本会记录日志并继续执行

## 日志说明

脚本会生成详细的日志文件 `category_fetch.log`，包含：
- 请求状态
- 获取进度
- 错误信息
- 统计数据

## 故障排除

### 常见问题

1. **401 Unauthorized**
   - 检查授权token是否正确
   - 确认token是否已过期

2. **404 Not Found**
   - 检查API URL是否正确
   - 确认平台代码是否支持

3. **网络超时**
   - 检查网络连接
   - 适当增加请求间隔

4. **数据不完整**
   - 检查日志文件中的错误信息
   - 重新运行脚本获取缺失数据

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多平台
- 数据去重和验证
- 增量更新机制
- 数据库存储
- Web界面展示

## 许可证

MIT License
