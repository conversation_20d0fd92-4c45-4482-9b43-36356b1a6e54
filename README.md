# 聚水潭平台类目信息获取工具

这是一个用于获取聚水潭平台类目信息的Python脚本，支持递归获取抖店、小红书、快手三个平台的完整类目树结构。

## 功能特性

- 🔄 **递归获取**: 自动递归获取完整的类目树结构
- 🏪 **多平台支持**: 支持抖店(TouTiaoFXG)、小红书(Xiaohs)、快手(KWaiShop)三个平台
- 💾 **数据保存**: 自动保存类目数据为JSON格式
- 📊 **统计信息**: 提供类目数量统计
- 🛡️ **错误处理**: 完善的错误处理和重试机制
- 📝 **日志记录**: 详细的操作日志记录
- 🔗 **真实API**: 基于聚水潭真实API接口开发

## 文件结构

```
├── category_fetcher.py      # 主程序文件
├── config.py               # 配置文件
├── jst_category_example.py # 聚水潭API使用示例
├── example_usage.py        # 通用使用示例
├── README.md               # 使用说明
├── requirements.txt        # 依赖包列表
├── data/                   # 数据输出目录
│   ├── 抖店_categories.json
│   ├── 小红书_categories.json
│   └── 快手_categories.json
└── category_fetch.log      # 日志文件
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

在使用前，请先修改 `config.py` 文件中的配置：

```python
API_CONFIG = {
    'BASE_URL': 'https://goods.scm121.com',     # 聚水潭API域名
    'AUTHORIZATION': 'your-jwt-token',          # 替换为实际的JWT token
    'SHOP_ID': 'your-shop-id',                  # 替换为实际的店铺ID
    'REQUEST_INTERVAL': 0.5,                    # 请求间隔
    'PLATFORM_INTERVAL': 2.0                   # 平台间请求间隔
}
```

### 获取JWT Token和店铺ID

1. **JWT Token**: 从您的聚水潭系统中获取，通常在登录后的请求头中可以找到
2. **店铺ID**: 在聚水潭系统的店铺管理中可以找到对应的店铺ID

## 使用方法

### 方法一：直接运行主程序

```bash
python category_fetcher.py
```

### 方法二：使用配置文件

修改 `category_fetcher.py` 中的配置导入：

```python
from config import API_CONFIG

def main():
    fetcher = CategoryFetcher(
        API_CONFIG['BASE_URL'], 
        API_CONFIG['AUTHORIZATION']
    )
    fetcher.fetch_all_platforms()
```

### 方法三：单独获取某个平台

```python
from category_fetcher import CategoryFetcher

# 创建获取器
fetcher = CategoryFetcher("https://goods.scm121.com", "your-jwt-token", "your-shop-id")

# 只获取小红书类目
categories = fetcher.fetch_categories_recursive('Xiaohs')
fetcher.save_categories('Xiaohs', categories)
```

### 方法四：使用示例脚本

运行聚水潭专用示例脚本：

```bash
python jst_category_example.py
```

该脚本提供了多种使用示例，包括API连接测试、单平台获取、特定类目获取等。

## API接口说明

本工具使用聚水潭的类目查询API接口：

### 获取类目树
```
GET /api/goods/platform/category/queryCategoryTreeByPid
```

**参数说明:**
- `shopType`: 平台类型 (Fxg/Xiaohs/Kwaishop)
- `pid`: 父类目ID，0表示顶级类目
- `shopId`: 店铺ID

**请求示例:**
```bash
curl --location --request GET 'https://goods.scm121.com/api/goods/platform/category/queryCategoryTreeByPid?shopType=Xiaohs&pid=0&shopId=18599275' \
--header 'authorization: your-jwt-token'
```

### 平台代码说明
- `Fxg`: 抖店（聚水潭）
- `Xiaohs`: 小红书（聚水潭）
- `Kwaishop`: 快手（聚水潭）

## 输出数据格式

生成的JSON文件结构如下：

```json
{
  "shop_type": "Xiaohs",
  "platform_name": "小红书",
  "shop_id": "18599275",
  "total_count": 1250,
  "categories": [
    {
      "id": "A91CC2B701CA49A49AE2BB281A4B459C",
      "categoryId": "65f9975b3e946300016b33e0",
      "categoryName": "文物商店/拍卖行",
      "platform": "Xiaohs",
      "pid": "0",
      "level": "1",
      "isParent": "N",
      "longCategoryName": "文物商店/拍卖行",
      "longCode": "000",
      "sorted": 1,
      "children": []
    },
    {
      "id": "B42AC100569D49069B888E32A0AAE2CA",
      "categoryId": "65f994b73e946300016aecfc",
      "categoryName": "笔记本电脑",
      "platform": "Xiaohs",
      "pid": "0",
      "level": "1",
      "isParent": "Y",
      "longCategoryName": "笔记本电脑",
      "longCode": "001",
      "sorted": 1,
      "children": [...]
    }
  ]
}
```

### 字段说明
- `id`: 内部ID
- `categoryId`: 平台类目ID
- `categoryName`: 类目名称
- `platform`: 平台代码
- `pid`: 父类目ID
- `level`: 类目层级
- `isParent`: 是否有子类目 (Y:是, N:否)
- `longCategoryName`: 完整类目名称
- `longCode`: 类目编码
- `sorted`: 排序值
- `children`: 子类目列表

## 注意事项

1. **API限制**: 请注意API的调用频率限制，脚本已内置请求间隔控制
2. **授权token**: 确保使用有效的授权token
3. **网络稳定**: 建议在网络稳定的环境下运行
4. **数据量**: 完整的类目树可能包含大量数据，请确保有足够的存储空间
5. **错误处理**: 如遇到网络错误，脚本会记录日志并继续执行

## 日志说明

脚本会生成详细的日志文件 `category_fetch.log`，包含：
- 请求状态
- 获取进度
- 错误信息
- 统计数据

## 故障排除

### 常见问题

1. **401 Unauthorized**
   - 检查授权token是否正确
   - 确认token是否已过期

2. **404 Not Found**
   - 检查API URL是否正确
   - 确认平台代码是否支持

3. **网络超时**
   - 检查网络连接
   - 适当增加请求间隔

4. **数据不完整**
   - 检查日志文件中的错误信息
   - 重新运行脚本获取缺失数据

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多平台
- 数据去重和验证
- 增量更新机制
- 数据库存储
- Web界面展示

## 许可证

MIT License
