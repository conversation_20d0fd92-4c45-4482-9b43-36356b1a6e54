# 聚水潭类目获取工具使用说明

## 概述

本工具支持从聚水潭API获取四个电商平台的类目信息：
- **抖店** (TouTiaoFXG) - 不需要shopId
- **小红书** (Xiaohs) - 需要shopId
- **快手** (KWaiShop) - 不需要shopId  
- **视频号小店** (WXChannel) - 不需要shopId

## 快速开始

### 1. 配置认证信息

编辑 `config.py` 文件，填入您的JWT token和店铺ID：

```python
API_CONFIG = {
    'BASE_URL': 'https://goods.scm121.com',
    'AUTHORIZATION': 'your-jwt-token-here',  # 替换为您的JWT token
    'SHOP_ID': 'your-shop-id-here',         # 小红书需要，其他平台可选
    'REQUEST_INTERVAL': 0.5,
    'PLATFORM_INTERVAL': 2.0
}
```

### 2. 运行方式

#### 方式一：交互式获取（推荐）

```bash
python category_fetcher.py
```

程序会显示平台选择菜单，您可以选择：
- 单个平台获取
- 所有平台获取

#### 方式二：单平台获取工具

```bash
# 交互式模式
python single_platform_fetcher.py

# 命令行模式 - 获取抖店类目
python single_platform_fetcher.py -p TouTiaoFXG -t "your-jwt-token"

# 命令行模式 - 获取小红书类目（需要shopId）
python single_platform_fetcher.py -p Xiaohs -t "your-jwt-token" -s "your-shop-id"

# 列出所有支持的平台
python single_platform_fetcher.py --list-platforms
```

#### 方式三：示例和测试

```bash
# 运行示例脚本
python jst_category_example.py

# 运行API测试工具
python test_all_platforms.py
```

## 输出文件

获取成功后，会在当前目录生成JSON文件：
- `categories_TouTiaoFXG.json` - 抖店类目
- `categories_Xiaohs.json` - 小红书类目
- `categories_KWaiShop.json` - 快手类目
- `categories_WXChannel.json` - 视频号小店类目

## 平台参数差异

| 平台 | 平台代码 | 是否需要shopId | 说明 |
|------|----------|----------------|------|
| 抖店 | TouTiaoFXG | ❌ | 抖音电商平台 |
| 小红书 | Xiaohs | ✅ | 需要提供店铺ID |
| 快手 | KWaiShop | ❌ | 快手电商平台 |
| 视频号小店 | WXChannel | ❌ | 微信视频号小店 |

## 获取JWT Token

1. 登录聚水潭后台
2. 打开浏览器开发者工具 (F12)
3. 切换到 Network 标签
4. 刷新页面或进行任意操作
5. 在请求头中找到 `authorization` 字段
6. 复制完整的JWT token（以 `eyJ` 开头）

## 获取店铺ID

小红书平台需要shopId参数：
1. 在聚水潭后台进入小红书店铺管理
2. 查看URL或页面信息获取shopId
3. 或在API请求中查看shopId参数

## 常见问题

### Q: 获取失败怎么办？
A: 检查以下几点：
1. JWT token是否正确且未过期
2. 小红书是否提供了shopId
3. 网络连接是否正常
4. API接口是否可访问

### Q: 如何获取特定类目的子类目？
A: 使用 `get_categories_by_pid()` 方法，传入父类目ID：

```python
fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)
sub_categories = fetcher.get_categories_by_pid('TouTiaoFXG', 'parent_category_id')
```

### Q: 如何控制请求频率？
A: 在 `config.py` 中调整间隔时间：
- `REQUEST_INTERVAL`: 单个请求间隔（秒）
- `PLATFORM_INTERVAL`: 平台间隔（秒）

### Q: 支持哪些输出格式？
A: 目前支持JSON格式输出，包含完整的类目层级结构。

## API接口说明

### 接口地址
```
GET https://goods.scm121.com/api/goods/platform/category/queryCategoryTreeByPid
```

### 请求参数
- `shopType`: 平台代码（必需）
- `pid`: 父类目ID，0表示顶级类目（必需）
- `shopId`: 店铺ID（小红书必需，其他平台可选）

### 请求头
- `authorization`: JWT认证token（必需）
- `accept`: `*/*`

### 响应格式
```json
{
  "success": true,
  "data": [
    {
      "id": "类目ID",
      "categoryId": "类目编码",
      "categoryName": "类目名称",
      "pid": "父类目ID",
      "level": "层级",
      "isParent": "是否有子类目",
      "longCategoryName": "完整类目名称",
      "longCode": "完整类目编码",
      "sorted": 排序号
    }
  ]
}
```

## 技术支持

如遇问题，请检查：
1. 日志输出信息
2. 网络连接状态
3. 认证信息是否正确
4. API接口是否正常

更多技术细节请参考源代码注释。
