CREATE TABLE `yt_category`
(
    `id`            varchar(64) NOT NULL COMMENT '类目ID',
    `platform`      varchar(16) NOT NULL COMMENT '平台(FXG:抖店 SPHXD:视频号小店 KWAISHOP:快手 YHT:源汇通(同抖店) XHS:小红书)',
    `name`          varchar(64)          DEFAULT NULL COMMENT '类目名称',
    `parent_id`     varchar(64)          DEFAULT NULL COMMENT '父类目',
    `level`         tinyint              DEFAULT NULL COMMENT '类目等级',
    `leaf`          tinyint              DEFAULT NULL COMMENT '是否叶子节点(0:否 1:是)',
    `image_url`     varchar(255)         DEFAULT NULL COMMENT '封面图url',
    `show_status`   tinyint     NOT NULL DEFAULT '0' COMMENT '是否显示(0:否, 1:是)',
    `delete_flag`   tinyint     NOT NULL DEFAULT '0' COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`    bigint               DEFAULT NULL COMMENT '创建人',
    `creator_name`  varchar(255)         DEFAULT NULL COMMENT '创建人名字',
    `modifier_id`   bigint               DEFAULT NULL COMMENT '修改操作人',
    `modifier_name` varchar(255)         DEFAULT NULL COMMENT '修改操作人名字',
    `created`       datetime             DEFAULT NULL COMMENT '创建时间',
    `updated`       datetime             DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`, `platform`) USING BTREE
) ENGINE=InnoDB COMMENT='类目表';