#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单平台类目获取工具
支持单独获取指定平台的类目信息
"""

import sys
import argparse
from category_fetcher import CategoryFetcher
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_platform_info():
    """获取支持的平台信息"""
    platforms = {
        'TouTiaoFXG': {
            'name': '抖店',
            'need_shop_id': False,
            'description': '抖音电商平台（聚水潭）'
        },
        'Xiaohs': {
            'name': '小红书',
            'need_shop_id': True,
            'description': '小红书电商平台（聚水潭）'
        },
        'KWaiShop': {
            'name': '快手',
            'need_shop_id': False,
            'description': '快手电商平台（聚水潭）'
        },
        'WXChannel': {
            'name': '视频号小店',
            'need_shop_id': False,
            'description': '微信视频号小店（聚水潭）'
        }
    }
    return platforms

def fetch_platform_categories(platform_code: str, authorization: str, shop_id: str = ""):
    """
    获取指定平台的类目信息
    
    Args:
        platform_code: 平台代码
        authorization: JWT认证token
        shop_id: 店铺ID（小红书需要）
    """
    BASE_URL = "https://goods.scm121.com"
    
    platforms = get_platform_info()
    
    if platform_code not in platforms:
        logger.error(f"不支持的平台代码: {platform_code}")
        logger.info("支持的平台:")
        for code, info in platforms.items():
            need_shop_id = "需要shopId" if info['need_shop_id'] else "无需shopId"
            logger.info(f"  {code}: {info['name']} - {need_shop_id}")
        return False
    
    platform_info = platforms[platform_code]
    platform_name = platform_info['name']
    
    # 检查shopId要求
    if platform_info['need_shop_id'] and not shop_id:
        logger.error(f"{platform_name}需要提供shopId参数")
        return False
    
    logger.info(f"开始获取{platform_name}({platform_code})类目信息...")
    logger.info(f"需要shopId: {platform_info['need_shop_id']}")
    
    try:
        # 创建获取器
        fetcher = CategoryFetcher(BASE_URL, authorization, shop_id)
        
        # 获取类目信息
        categories = fetcher.fetch_categories_recursive(platform_code)
        
        if categories:
            fetcher.save_categories(platform_code, categories)
            logger.info(f"✅ {platform_name}类目信息获取完成！")
            logger.info(f"共获取到 {len(categories)} 个类目")
            return True
        else:
            logger.error(f"❌ 未获取到{platform_name}类目信息")
            return False
            
    except Exception as e:
        logger.error(f"❌ 获取{platform_name}类目信息失败: {e}")
        return False

def interactive_mode():
    """交互式模式"""
    platforms = get_platform_info()
    
    print("\n聚水潭单平台类目获取工具")
    print("=" * 50)
    print("支持的平台:")
    
    platform_list = list(platforms.items())
    for i, (code, info) in enumerate(platform_list, 1):
        need_shop_id = "需要shopId" if info['need_shop_id'] else "无需shopId"
        print(f"{i}. {info['name']} ({code}) - {need_shop_id}")
    
    print("0. 退出")
    
    while True:
        try:
            choice = input(f"\n请选择平台 (0-{len(platform_list)}): ").strip()
            
            if choice == "0":
                print("退出程序")
                return
            elif choice.isdigit() and 1 <= int(choice) <= len(platform_list):
                platform_code = platform_list[int(choice) - 1][0]
                platform_info = platform_list[int(choice) - 1][1]
                break
            else:
                print("无效选择，请重新输入")
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            return
    
    # 获取认证信息
    print(f"\n选择的平台: {platform_info['name']} ({platform_code})")
    
    authorization = input("请输入JWT认证token: ").strip()
    if not authorization:
        print("认证token不能为空")
        return
    
    shop_id = ""
    if platform_info['need_shop_id']:
        shop_id = input("请输入店铺ID (shopId): ").strip()
        if not shop_id:
            print("该平台需要提供店铺ID")
            return
    
    # 执行获取
    success = fetch_platform_categories(platform_code, authorization, shop_id)
    
    if success:
        print(f"\n🎉 {platform_info['name']}类目信息获取成功！")
        print(f"输出文件: categories_{platform_code}.json")
    else:
        print(f"\n❌ {platform_info['name']}类目信息获取失败！")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='聚水潭单平台类目获取工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
支持的平台代码:
  TouTiaoFXG  - 抖店（无需shopId）
  Xiaohs      - 小红书（需要shopId）
  KWaiShop    - 快手（无需shopId）
  WXChannel   - 视频号小店（无需shopId）

使用示例:
  # 交互式模式
  python single_platform_fetcher.py
  
  # 命令行模式 - 获取抖店类目
  python single_platform_fetcher.py -p TouTiaoFXG -t "your-jwt-token"
  
  # 命令行模式 - 获取小红书类目（需要shopId）
  python single_platform_fetcher.py -p Xiaohs -t "your-jwt-token" -s "your-shop-id"
        """
    )
    
    parser.add_argument('-p', '--platform', 
                       help='平台代码 (TouTiaoFXG/Xiaohs/KWaiShop/WXChannel)')
    parser.add_argument('-t', '--token', 
                       help='JWT认证token')
    parser.add_argument('-s', '--shop-id', 
                       help='店铺ID（小红书需要）')
    parser.add_argument('--list-platforms', action='store_true',
                       help='列出所有支持的平台')
    
    args = parser.parse_args()
    
    # 列出平台信息
    if args.list_platforms:
        platforms = get_platform_info()
        print("支持的平台:")
        for code, info in platforms.items():
            need_shop_id = "需要shopId" if info['need_shop_id'] else "无需shopId"
            print(f"  {code}: {info['name']} - {need_shop_id}")
            print(f"    {info['description']}")
        return
    
    # 命令行模式
    if args.platform and args.token:
        success = fetch_platform_categories(args.platform, args.token, args.shop_id or "")
        sys.exit(0 if success else 1)
    
    # 交互式模式
    if not args.platform or not args.token:
        interactive_mode()
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
