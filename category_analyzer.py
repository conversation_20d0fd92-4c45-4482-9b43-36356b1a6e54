#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类目数据分析工具
用于分析已获取的类目数据，检查完整性和统计信息
"""

import json
import os
from typing import Dict, List, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CategoryAnalyzer:
    """类目数据分析器"""
    
    def __init__(self):
        self.data_dir = "data"
    
    def load_category_data(self, platform_code: str) -> Dict[str, Any]:
        """
        加载指定平台的类目数据
        
        Args:
            platform_code: 平台代码
            
        Returns:
            类目数据字典
        """
        filename = f"categories_{platform_code}.json"
        filepath = os.path.join(self.data_dir, filename)
        
        if not os.path.exists(filepath):
            logger.error(f"文件不存在: {filepath}")
            return {}
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载 {platform_code} 类目数据")
            return data
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            return {}
    
    def analyze_category_structure(self, categories: List[Dict], depth: int = 0) -> Dict[str, Any]:
        """
        分析类目结构
        
        Args:
            categories: 类目列表
            depth: 当前深度
            
        Returns:
            分析结果
        """
        stats = {
            'total_count': 0,
            'depth_stats': {},
            'parent_count': 0,
            'leaf_count': 0,
            'max_depth': depth,
            'categories_by_depth': {}
        }
        
        current_depth_count = len(categories)
        stats['depth_stats'][depth] = current_depth_count
        stats['categories_by_depth'][depth] = []
        
        for category in categories:
            stats['total_count'] += 1
            
            # 记录当前深度的类目信息
            stats['categories_by_depth'][depth].append({
                'id': category.get('categoryId', ''),
                'name': category.get('categoryName', ''),
                'isParent': category.get('isParent', 'N')
            })
            
            if category.get('isParent') == 'Y':
                stats['parent_count'] += 1
                
                # 递归分析子类目
                children = category.get('children', [])
                if children:
                    child_stats = self.analyze_category_structure(children, depth + 1)
                    
                    # 合并统计信息
                    stats['total_count'] += child_stats['total_count']
                    stats['parent_count'] += child_stats['parent_count']
                    stats['leaf_count'] += child_stats['leaf_count']
                    stats['max_depth'] = max(stats['max_depth'], child_stats['max_depth'])
                    
                    # 合并深度统计
                    for d, count in child_stats['depth_stats'].items():
                        stats['depth_stats'][d] = stats['depth_stats'].get(d, 0) + count
                    
                    # 合并各深度类目信息
                    for d, cats in child_stats['categories_by_depth'].items():
                        if d not in stats['categories_by_depth']:
                            stats['categories_by_depth'][d] = []
                        stats['categories_by_depth'][d].extend(cats)
                else:
                    # 标记为父类目但没有子类目的情况
                    logger.warning(f"类目 '{category.get('categoryName')}' 标记为父类目但没有子类目")
            else:
                stats['leaf_count'] += 1
        
        return stats
    
    def check_missing_categories(self, platform_code: str) -> Dict[str, Any]:
        """
        检查可能遗漏的类目
        
        Args:
            platform_code: 平台代码
            
        Returns:
            检查结果
        """
        data = self.load_category_data(platform_code)
        if not data:
            return {}
        
        categories = data.get('categories', [])
        stats = self.analyze_category_structure(categories)
        
        # 查找标记为父类目但没有子类目的情况
        missing_info = {
            'platform': platform_code,
            'total_categories': stats['total_count'],
            'parent_categories': stats['parent_count'],
            'leaf_categories': stats['leaf_count'],
            'max_depth': stats['max_depth'],
            'depth_distribution': stats['depth_stats'],
            'potential_missing': []
        }
        
        # 检查每个深度的类目
        for depth, categories_at_depth in stats['categories_by_depth'].items():
            for cat in categories_at_depth:
                if cat['isParent'] == 'Y':
                    # 这里可以添加更多检查逻辑
                    missing_info['potential_missing'].append({
                        'depth': depth,
                        'category_id': cat['id'],
                        'category_name': cat['name'],
                        'reason': '标记为父类目，需要验证是否有子类目'
                    })
        
        return missing_info
    
    def compare_platforms(self) -> Dict[str, Any]:
        """
        比较不同平台的类目数量
        
        Returns:
            比较结果
        """
        platforms = ['TouTiaoFXG', 'Xiaohs', 'KWaiShop', 'WXChannel']
        platform_names = {
            'TouTiaoFXG': '抖店',
            'Xiaohs': '小红书',
            'KWaiShop': '快手',
            'WXChannel': '视频号小店'
        }
        
        comparison = {
            'platforms': {},
            'summary': {}
        }
        
        total_all = 0
        for platform_code in platforms:
            data = self.load_category_data(platform_code)
            if data:
                categories = data.get('categories', [])
                stats = self.analyze_category_structure(categories)
                
                comparison['platforms'][platform_code] = {
                    'name': platform_names.get(platform_code, platform_code),
                    'total_count': stats['total_count'],
                    'parent_count': stats['parent_count'],
                    'leaf_count': stats['leaf_count'],
                    'max_depth': stats['max_depth'],
                    'depth_stats': stats['depth_stats']
                }
                
                total_all += stats['total_count']
            else:
                comparison['platforms'][platform_code] = {
                    'name': platform_names.get(platform_code, platform_code),
                    'error': '数据文件不存在或加载失败'
                }
        
        comparison['summary']['total_categories_all_platforms'] = total_all
        
        return comparison
    
    def generate_report(self, platform_code: str = None):
        """
        生成分析报告
        
        Args:
            platform_code: 平台代码，None表示分析所有平台
        """
        if platform_code:
            # 单平台分析
            logger.info(f"分析 {platform_code} 平台类目数据...")
            missing_info = self.check_missing_categories(platform_code)
            
            if missing_info:
                print(f"\n{'='*60}")
                print(f"{missing_info['platform']} 类目分析报告")
                print(f"{'='*60}")
                print(f"总类目数量: {missing_info['total_categories']}")
                print(f"父类目数量: {missing_info['parent_categories']}")
                print(f"叶子类目数量: {missing_info['leaf_categories']}")
                print(f"最大深度: {missing_info['max_depth']}")
                print(f"\n各深度类目分布:")
                for depth, count in missing_info['depth_distribution'].items():
                    print(f"  深度 {depth}: {count} 个类目")
                
                if missing_info['potential_missing']:
                    print(f"\n可能需要检查的类目 ({len(missing_info['potential_missing'])} 个):")
                    for item in missing_info['potential_missing'][:10]:  # 只显示前10个
                        print(f"  - {item['category_name']} (ID: {item['category_id']}, 深度: {item['depth']})")
                    if len(missing_info['potential_missing']) > 10:
                        print(f"  ... 还有 {len(missing_info['potential_missing']) - 10} 个")
        else:
            # 所有平台比较
            logger.info("比较所有平台类目数据...")
            comparison = self.compare_platforms()
            
            print(f"\n{'='*60}")
            print("所有平台类目数量比较")
            print(f"{'='*60}")
            
            for platform_code, info in comparison['platforms'].items():
                if 'error' in info:
                    print(f"{info['name']}: {info['error']}")
                else:
                    print(f"{info['name']}: {info['total_count']} 个类目 (父类目: {info['parent_count']}, 叶子: {info['leaf_count']}, 最大深度: {info['max_depth']})")
            
            print(f"\n所有平台总计: {comparison['summary']['total_categories_all_platforms']} 个类目")

def main():
    """主函数"""
    analyzer = CategoryAnalyzer()
    
    print("聚水潭类目数据分析工具")
    print("=" * 50)
    print("请选择分析选项:")
    print("1. 分析抖店类目")
    print("2. 分析小红书类目")
    print("3. 分析快手类目")
    print("4. 分析视频号小店类目")
    print("5. 比较所有平台")
    print("0. 退出")
    
    platform_map = {
        '1': 'TouTiaoFXG',
        '2': 'Xiaohs',
        '3': 'KWaiShop',
        '4': 'WXChannel'
    }
    
    while True:
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "0":
            print("退出程序")
            break
        elif choice in platform_map:
            analyzer.generate_report(platform_map[choice])
        elif choice == "5":
            analyzer.generate_report()
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
