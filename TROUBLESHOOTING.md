# 类目获取问题排查指南

## 问题：类目数量不完整

如果您发现获取的类目数量比预期少（如只有3912条，但应该有更多），请按以下步骤排查：

### 1. 使用增强版获取工具

```bash
python enhanced_fetcher.py
```

增强版工具提供：
- 详细的获取进度日志
- 重试机制
- 完整性验证
- 详细的统计报告

### 2. 分析现有数据

```bash
python category_analyzer.py
```

选择对应平台进行分析，查看：
- 总类目数量统计
- 各深度类目分布
- 可能遗漏的类目
- 标记为父类目但没有子类目的情况

### 3. 检查常见问题

#### 3.1 网络超时或API限制
- 检查网络连接是否稳定
- 适当增加请求间隔时间
- 在 `config.py` 中调整 `REQUEST_INTERVAL` 和 `PLATFORM_INTERVAL`

#### 3.2 认证token过期
- 检查JWT token是否仍然有效
- 重新获取最新的token

#### 3.3 递归深度限制
- 检查是否有类目层级过深导致的截断
- 查看日志中的递归深度信息

#### 3.4 API返回数据不完整
- 某些父类目可能标记错误
- API可能对单次请求返回数量有限制

### 4. 使用详细日志模式

修改 `category_fetcher.py` 中的日志级别：

```python
logging.basicConfig(level=logging.DEBUG)
```

这将显示更详细的获取过程信息。

### 5. 手动验证特定类目

如果发现某个类目的子类目缺失，可以手动验证：

```python
from category_fetcher import CategoryFetcher

# 配置
BASE_URL = "https://goods.scm121.com"
AUTHORIZATION = "your-jwt-token"
SHOP_ID = "your-shop-id"  # 小红书需要

fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)

# 手动获取特定类目的子类目
categories = fetcher.get_categories_by_pid('TouTiaoFXG', 'specific_category_id')
print(f"获取到 {len(categories)} 个子类目")
```

### 6. 比较不同获取方式

尝试使用不同的工具获取同一平台的数据：

```bash
# 方式1：标准获取
python category_fetcher.py

# 方式2：增强版获取
python enhanced_fetcher.py

# 方式3：单平台获取
python single_platform_fetcher.py -p TouTiaoFXG -t "your-token"
```

比较结果差异。

### 7. 检查API响应

在获取过程中，注意观察：
- API是否返回 `success: true`
- `data` 字段是否包含预期的类目数据
- 是否有错误信息或警告

### 8. 分批获取验证

对于大量类目，可以考虑分批获取：

```python
# 先获取顶级类目
top_categories = fetcher.get_categories_by_pid('TouTiaoFXG', '0')
print(f"顶级类目数量: {len(top_categories)}")

# 逐个获取每个顶级类目的子类目
for cat in top_categories:
    if cat.isParent == 'Y':
        children = fetcher.get_categories_by_pid('TouTiaoFXG', cat.categoryId)
        print(f"类目 '{cat.categoryName}' 的子类目数量: {len(children)}")
```

### 9. 常见的数量差异原因

1. **API分页限制**：某些API可能对单次返回的数据量有限制
2. **权限限制**：不同的账号可能看到不同的类目
3. **平台差异**：不同平台的类目结构和数量本身就不同
4. **时间差异**：类目数据可能会随时间变化
5. **递归终止条件**：某些类目可能因为特殊情况没有被正确递归

### 10. 优化建议

1. **增加请求间隔**：避免触发API限制
2. **添加重试机制**：处理网络波动
3. **保存中间结果**：避免重复获取
4. **分段获取**：对于大型类目树，考虑分段处理
5. **验证数据完整性**：获取完成后进行数据验证

### 11. 联系技术支持

如果以上方法都无法解决问题，请提供：
- 使用的平台代码
- 获取到的类目数量
- 预期的类目数量
- 详细的错误日志
- 使用的JWT token（脱敏处理）

## 快速诊断命令

```bash
# 1. 分析现有数据
python category_analyzer.py

# 2. 使用增强版重新获取
python enhanced_fetcher.py

# 3. 比较所有平台数据
python category_analyzer.py  # 选择选项5

# 4. 测试API连接
python test_all_platforms.py  # 选择选项1
```

## 预期类目数量参考

根据经验，各平台的大致类目数量：
- **抖店**：通常有数千个类目
- **小红书**：类目数量相对较少，但结构较深
- **快手**：类目数量中等
- **视频号小店**：作为新平台，类目数量可能较少

具体数量会根据平台更新和账号权限有所不同。
