#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有平台API调用的脚本
验证抖店、小红书、快手三个平台的API接口
"""

from category_fetcher import CategoryFetcher
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_platform_api(platform_code: str, platform_name: str, need_shop_id: bool = False):
    """
    测试单个平台的API调用
    
    Args:
        platform_code: 平台代码
        platform_name: 平台名称
        need_shop_id: 是否需要shopId参数
    """
    BASE_URL = "https://goods.scm121.com"
    
    # 请替换为您的实际JWT token
    AUTHORIZATION = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-actual-token"
    
    # 店铺ID（仅小红书需要）
    SHOP_ID = "18599275" if need_shop_id else ""
    
    logger.info(f"\n{'='*60}")
    logger.info(f"测试 {platform_name} ({platform_code}) API")
    logger.info(f"需要shopId: {need_shop_id}")
    logger.info(f"{'='*60}")
    
    try:
        # 创建获取器
        fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)
        
        # 测试获取顶级类目
        logger.info(f"1. 测试获取{platform_name}顶级类目...")
        top_categories = fetcher.get_categories_by_pid(platform_code, "0")
        
        if top_categories:
            logger.info(f"✅ 成功获取到{len(top_categories)}个顶级类目")
            
            # 显示前5个类目
            logger.info("前5个类目:")
            for i, cat in enumerate(top_categories[:5]):
                logger.info(f"  {i+1}. {cat.categoryName} (ID: {cat.categoryId}, isParent: {cat.isParent})")
            
            # 测试获取子类目（如果有的话）
            parent_category = None
            for cat in top_categories:
                if cat.isParent == "Y":
                    parent_category = cat
                    break
            
            if parent_category:
                logger.info(f"\n2. 测试获取子类目 (父类目: {parent_category.categoryName})...")
                sub_categories = fetcher.get_categories_by_pid(platform_code, parent_category.categoryId)
                
                if sub_categories:
                    logger.info(f"✅ 成功获取到{len(sub_categories)}个子类目")
                    for i, cat in enumerate(sub_categories[:3]):
                        logger.info(f"  {i+1}. {cat.categoryName} (ID: {cat.categoryId})")
                else:
                    logger.warning("⚠️ 未获取到子类目")
            else:
                logger.info("2. 没有找到有子类目的父类目")
                
        else:
            logger.error(f"❌ 未获取到{platform_name}顶级类目")
            
    except Exception as e:
        logger.error(f"❌ 测试{platform_name}API失败: {e}")

def test_all_platforms():
    """测试所有平台的API"""
    
    platforms = [
        {
            'code': 'TouTiaoFXG',
            'name': '抖店',
            'need_shop_id': False,
            'description': '抖音电商平台，不需要shopId参数'
        },
        {
            'code': 'Xiaohs',
            'name': '小红书',
            'need_shop_id': True,
            'description': '小红书电商平台，需要shopId参数'
        },
        {
            'code': 'KWaiShop',
            'name': '快手',
            'need_shop_id': False,
            'description': '快手电商平台，不需要shopId参数'
        },
        {
            'code': 'WXChannel',
            'name': '视频号小店',
            'need_shop_id': False,
            'description': '微信视频号小店，不需要shopId参数'
        }
    ]
    
    logger.info("开始测试所有平台API...")
    logger.info(f"测试时间: {__import__('datetime').datetime.now()}")
    
    for platform in platforms:
        logger.info(f"\n📝 {platform['description']}")
        test_platform_api(
            platform['code'], 
            platform['name'], 
            platform['need_shop_id']
        )
    
    logger.info(f"\n{'='*60}")
    logger.info("所有平台API测试完成！")
    logger.info(f"{'='*60}")

def test_api_differences():
    """测试API调用差异"""
    
    BASE_URL = "https://goods.scm121.com"
    AUTHORIZATION = "your-jwt-token"  # 请替换
    SHOP_ID = "18599275"
    
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)
    
    logger.info("\n🔍 API调用差异分析:")
    logger.info("-" * 40)
    
    # 测试不同平台的参数需求
    test_cases = [
        {
            'platform': 'TouTiaoFXG',
            'name': '抖店',
            'expected_params': ['shopType', 'pid']
        },
        {
            'platform': 'Xiaohs',
            'name': '小红书',
            'expected_params': ['shopType', 'pid', 'shopId']
        },
        {
            'platform': 'KWaiShop',
            'name': '快手',
            'expected_params': ['shopType', 'pid']
        },
        {
            'platform': 'WXChannel',
            'name': '视频号小店',
            'expected_params': ['shopType', 'pid']
        }
    ]
    
    for case in test_cases:
        logger.info(f"\n{case['name']} ({case['platform']}):")
        logger.info(f"  预期参数: {', '.join(case['expected_params'])}")
        
        # 构建参数
        params = {
            'shopType': case['platform'],
            'pid': '0'
        }
        
        if 'shopId' in case['expected_params']:
            params['shopId'] = SHOP_ID
            
        logger.info(f"  实际参数: {params}")

def generate_curl_commands():
    """生成所有平台的curl命令示例"""
    
    AUTHORIZATION = "your-jwt-token"  # 请替换
    SHOP_ID = "18599275"
    
    logger.info("\n📋 各平台curl命令示例:")
    logger.info("=" * 60)
    
    # 抖店
    logger.info("\n🎵 抖店 (TouTiaoFXG):")
    logger.info("curl --location --request GET 'https://goods.scm121.com/api/goods/platform/category/queryCategoryTreeByPid?shopType=TouTiaoFXG&pid=0' \\")
    logger.info(f"--header 'authorization: {AUTHORIZATION}' \\")
    logger.info("--header 'accept: */*'")
    
    # 小红书
    logger.info("\n📕 小红书 (Xiaohs):")
    logger.info(f"curl --location --request GET 'https://goods.scm121.com/api/goods/platform/category/queryCategoryTreeByPid?shopType=Xiaohs&pid=0&shopId={SHOP_ID}' \\")
    logger.info(f"--header 'authorization: {AUTHORIZATION}' \\")
    logger.info("--header 'accept: */*'")
    
    # 快手
    logger.info("\n⚡ 快手 (KWaiShop):")
    logger.info("curl --location --request GET 'https://goods.scm121.com/api/goods/platform/category/queryCategoryTreeByPid?shopType=KWaiShop&pid=0' \\")
    logger.info(f"--header 'authorization: {AUTHORIZATION}' \\")
    logger.info("--header 'accept: */*'")

    # 视频号小店
    logger.info("\n📺 视频号小店 (WXChannel):")
    logger.info("curl --location --request GET 'https://goods.scm121.com/api/goods/platform/category/queryCategoryTreeByPid?shopType=WXChannel&pid=0' \\")
    logger.info(f"--header 'authorization: {AUTHORIZATION}' \\")
    logger.info("--header 'accept: */*'")

def main():
    """主函数"""
    print("聚水潭平台API测试工具")
    print("=" * 50)
    print("请选择测试选项:")
    print("1. 测试所有平台API")
    print("2. 分析API调用差异")
    print("3. 生成curl命令示例")
    print("0. 退出")
    
    while True:
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == "0":
            print("退出程序")
            break
        elif choice == "1":
            test_all_platforms()
        elif choice == "2":
            test_api_differences()
        elif choice == "3":
            generate_curl_commands()
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
