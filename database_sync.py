#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库同步工具
将JSON类目数据同步到MySQL数据库中
"""

import json
import os
import pymysql
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import logging
from db_config import DATABASE_CONFIG, PLATFORM_MAPPING, PLATFORM_NAMES, SYNC_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CategoryDatabaseSync:
    """类目数据库同步器"""

    def __init__(self, db_config: Dict[str, Any] = None):
        """
        初始化数据库连接

        Args:
            db_config: 数据库配置，如果为None则使用默认配置
        """
        self.db_config = db_config or DATABASE_CONFIG
        self.connection = None
        self.data_dir = "data"
        self.batch_size = SYNC_CONFIG['batch_size']
        self.retry_count = SYNC_CONFIG['retry_count']
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def close_database(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def load_json_data(self, platform_code: str) -> Optional[Dict[str, Any]]:
        """
        加载JSON数据

        Args:
            platform_code: 平台代码

        Returns:
            JSON数据字典
        """
        platform_name = PLATFORM_NAMES.get(platform_code)
        if not platform_name:
            logger.error(f"不支持的平台代码: {platform_code}")
            return None

        filename = f"{platform_name}_categories.json"
        filepath = os.path.join(self.data_dir, filename)

        if not os.path.exists(filepath):
            logger.error(f"文件不存在: {filepath}")
            return None

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载 {platform_name} 数据，总计 {data.get('total_count', 0)} 个类目")
            return data
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            return None
    
    def category_exists(self, category_id: str, platform: str) -> bool:
        """
        检查类目是否已存在
        
        Args:
            category_id: 类目ID
            platform: 平台代码
            
        Returns:
            是否存在
        """
        try:
            with self.connection.cursor() as cursor:
                sql = "SELECT COUNT(*) FROM yt_category WHERE id = %s AND platform = %s"
                cursor.execute(sql, (category_id, platform))
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            logger.error(f"检查类目存在性失败: {e}")
            return False
    
    def insert_category(self, category: Dict[str, Any], platform: str) -> bool:
        """
        插入类目数据
        
        Args:
            category: 类目数据
            platform: 平台代码
            
        Returns:
            是否成功
        """
        try:
            with self.connection.cursor() as cursor:
                # 准备数据
                category_id = category.get('categoryId', '')
                name = category.get('categoryName', '')
                parent_id = category.get('pid', '0')
                level = int(category.get('level', 1))
                is_leaf = 1 if category.get('isParent', 'N') == 'N' else 0
                
                # 如果parent_id是'0'，设置为NULL
                if parent_id == '0':
                    parent_id = None
                
                current_time = datetime.now()
                
                sql = """
                INSERT INTO yt_category 
                (id, platform, name, parent_id, level, leaf, show_status, delete_flag, created, updated)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                values = (
                    category_id,
                    platform,
                    name,
                    parent_id,
                    level,
                    is_leaf,
                    1,  # show_status: 默认显示
                    0,  # delete_flag: 默认未删除
                    current_time,
                    current_time
                )
                
                cursor.execute(sql, values)
                return True
                
        except Exception as e:
            logger.error(f"插入类目失败 {category_id}: {e}")
            return False
    
    def process_categories_recursive(self, categories: List[Dict[str, Any]], platform: str) -> Dict[str, int]:
        """
        递归处理类目数据
        
        Args:
            categories: 类目列表
            platform: 平台代码
            
        Returns:
            处理统计信息
        """
        stats = {
            'total_processed': 0,
            'new_inserted': 0,
            'already_exists': 0,
            'failed': 0
        }
        
        for category in categories:
            stats['total_processed'] += 1
            category_id = category.get('categoryId', '')
            category_name = category.get('categoryName', '')
            
            # 检查是否已存在
            if self.category_exists(category_id, platform):
                stats['already_exists'] += 1
                logger.debug(f"类目已存在: {category_name} ({category_id})")
            else:
                # 插入新类目
                if self.insert_category(category, platform):
                    stats['new_inserted'] += 1
                    logger.info(f"新增类目: {category_name} ({category_id})")
                else:
                    stats['failed'] += 1
                    logger.error(f"插入失败: {category_name} ({category_id})")
            
            # 递归处理子类目
            children = category.get('children', [])
            if children:
                child_stats = self.process_categories_recursive(children, platform)
                # 合并统计信息
                for key in stats:
                    stats[key] += child_stats[key]
        
        return stats
    
    def sync_platform_data(self, platform_code: str) -> Dict[str, Any]:
        """
        同步单个平台的数据
        
        Args:
            platform_code: 平台代码
            
        Returns:
            同步结果
        """
        # 获取数据库平台代码
        db_platform = self.PLATFORM_MAPPING.get(platform_code)
        if not db_platform:
            return {
                'success': False,
                'error': f'不支持的平台代码: {platform_code}'
            }
        
        # 加载JSON数据
        data = self.load_json_data(platform_code)
        if not data:
            return {
                'success': False,
                'error': f'无法加载 {platform_code} 数据'
            }
        
        categories = data.get('categories', [])
        if not categories:
            return {
                'success': False,
                'error': f'{platform_code} 数据为空'
            }
        
        logger.info(f"开始同步 {platform_code} 数据到数据库...")
        
        try:
            # 开始事务
            self.connection.begin()
            
            # 处理类目数据
            stats = self.process_categories_recursive(categories, db_platform)
            
            # 提交事务
            self.connection.commit()
            
            result = {
                'success': True,
                'platform_code': platform_code,
                'db_platform': db_platform,
                'statistics': stats
            }
            
            logger.info(f"✅ {platform_code} 数据同步完成")
            logger.info(f"总处理: {stats['total_processed']}, 新增: {stats['new_inserted']}, 已存在: {stats['already_exists']}, 失败: {stats['failed']}")
            
            return result
            
        except Exception as e:
            # 回滚事务
            self.connection.rollback()
            logger.error(f"❌ {platform_code} 数据同步失败: {e}")
            return {
                'success': False,
                'platform_code': platform_code,
                'error': str(e)
            }
    
    def sync_all_platforms(self) -> Dict[str, Any]:
        """
        同步所有平台数据
        
        Returns:
            同步结果汇总
        """
        platforms = ['TouTiaoFXG', 'Xiaohs', 'KWaiShop', 'WXChannel']
        results = {}
        summary = {
            'total_platforms': len(platforms),
            'successful_platforms': 0,
            'failed_platforms': 0,
            'total_new_categories': 0,
            'total_processed_categories': 0
        }
        
        for platform_code in platforms:
            logger.info(f"\n{'='*60}")
            logger.info(f"同步平台: {platform_code}")
            logger.info(f"{'='*60}")
            
            result = self.sync_platform_data(platform_code)
            results[platform_code] = result
            
            if result['success']:
                summary['successful_platforms'] += 1
                stats = result['statistics']
                summary['total_new_categories'] += stats['new_inserted']
                summary['total_processed_categories'] += stats['total_processed']
            else:
                summary['failed_platforms'] += 1
        
        return {
            'summary': summary,
            'details': results
        }

def main():
    """主函数"""
    # 数据库配置
    db_config = {
        'host': input("请输入数据库主机地址 (默认: localhost): ").strip() or 'localhost',
        'port': int(input("请输入数据库端口 (默认: 3306): ").strip() or '3306'),
        'user': input("请输入数据库用户名: ").strip(),
        'password': input("请输入数据库密码: ").strip(),
        'database': input("请输入数据库名称: ").strip()
    }
    
    # 创建同步器
    sync_tool = CategoryDatabaseSync(db_config)
    
    try:
        # 连接数据库
        sync_tool.connect_database()
        
        print("\n数据库同步工具")
        print("=" * 50)
        print("请选择同步选项:")
        print("1. 同步抖店数据")
        print("2. 同步小红书数据")
        print("3. 同步快手数据")
        print("4. 同步视频号小店数据")
        print("5. 同步所有平台数据")
        print("0. 退出")
        
        platform_map = {
            '1': 'TouTiaoFXG',
            '2': 'Xiaohs',
            '3': 'KWaiShop',
            '4': 'WXChannel'
        }
        
        while True:
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == "0":
                print("退出程序")
                break
            elif choice in platform_map:
                result = sync_tool.sync_platform_data(platform_map[choice])
                if result['success']:
                    stats = result['statistics']
                    print(f"\n✅ 同步完成!")
                    print(f"总处理: {stats['total_processed']} 个类目")
                    print(f"新增: {stats['new_inserted']} 个")
                    print(f"已存在: {stats['already_exists']} 个")
                    print(f"失败: {stats['failed']} 个")
                else:
                    print(f"\n❌ 同步失败: {result['error']}")
                break
            elif choice == "5":
                print("\n开始同步所有平台数据...")
                results = sync_tool.sync_all_platforms()
                
                print(f"\n{'='*60}")
                print("同步结果汇总")
                print(f"{'='*60}")
                summary = results['summary']
                print(f"总平台数: {summary['total_platforms']}")
                print(f"成功: {summary['successful_platforms']}")
                print(f"失败: {summary['failed_platforms']}")
                print(f"总处理类目: {summary['total_processed_categories']}")
                print(f"总新增类目: {summary['total_new_categories']}")
                break
            else:
                print("无效选择，请重新输入")
                
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    finally:
        sync_tool.close_database()

if __name__ == "__main__":
    main()
