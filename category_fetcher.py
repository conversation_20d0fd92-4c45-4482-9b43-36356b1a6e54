#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
铺货助手平台类目信息获取脚本
支持递归获取抖店、小红书、快手三个平台的类目信息
"""

import requests
import json
import time
import os
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('category_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class CategoryInfo:
    """类目信息数据类"""
    id: str
    platform: str
    name: str
    imageUrl: str = ""
    parentId: str = ""
    level: int = 0
    leaf: int = 0
    showStatus: int = 0
    children: List['CategoryInfo'] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []

class CategoryFetcher:
    """类目信息获取器"""
    
    # 平台映射
    PLATFORMS = {
        'FXG': '抖店',
        'XHS': '小红书', 
        'KWAISHOP': '快手'
    }
    
    def __init__(self, base_url: str = "", authorization: str = ""):
        """
        初始化类目获取器
        
        Args:
            base_url: API基础URL
            authorization: 授权token
        """
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {authorization}',
            'Content-Type': 'application/json',
            'User-Agent': 'CategoryFetcher/1.0'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 请求间隔（秒）
        self.request_interval = 0.5
        
    def get_top_categories(self, platform: str) -> List[CategoryInfo]:
        """
        获取顶级类目列表
        
        Args:
            platform: 平台代码 (FXG/XHS/KWAISHOP)
            
        Returns:
            顶级类目列表
        """
        url = f"{self.base_url}/v1/enterprise/product/supply-category/top/list/{platform}"
        
        try:
            logger.info(f"获取{self.PLATFORMS.get(platform, platform)}顶级类目...")
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') != 0:
                logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                return []
                
            categories = []
            for item in data.get('data', []):
                category = CategoryInfo(
                    id=item.get('id', ''),
                    platform=item.get('platform', platform),
                    name=item.get('name', ''),
                    imageUrl=item.get('imageUrl', ''),
                    parentId=item.get('parentId', ''),
                    level=item.get('level', 0),
                    leaf=item.get('leaf', 0),
                    showStatus=item.get('showStatus', 0)
                )
                categories.append(category)
                
            logger.info(f"获取到{len(categories)}个{self.PLATFORMS.get(platform, platform)}顶级类目")
            return categories
            
        except requests.RequestException as e:
            logger.error(f"请求失败: {e}")
            return []
        except Exception as e:
            logger.error(f"处理响应失败: {e}")
            return []
    
    def get_category_tree(self, platform: str, parent_id: str) -> List[CategoryInfo]:
        """
        获取类目树信息
        
        Args:
            platform: 平台代码
            parent_id: 父类目ID
            
        Returns:
            子类目列表
        """
        url = f"{self.base_url}/v1/enterprise/product/supply-category/tree/{platform}/{parent_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') != 0:
                logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                return []
                
            categories = []
            for item in data.get('data', []):
                category = CategoryInfo(
                    id=item.get('id', ''),
                    platform=item.get('platform', platform),
                    name=item.get('name', ''),
                    imageUrl=item.get('imageUrl', ''),
                    parentId=item.get('parentId', ''),
                    level=item.get('level', 0),
                    leaf=item.get('leaf', 0),
                    showStatus=item.get('showStatus', 0)
                )
                categories.append(category)
                
            return categories
            
        except requests.RequestException as e:
            logger.error(f"请求失败: {e}")
            return []
        except Exception as e:
            logger.error(f"处理响应失败: {e}")
            return []
    
    def fetch_categories_recursive(self, platform: str, parent_category: Optional[CategoryInfo] = None) -> List[CategoryInfo]:
        """
        递归获取类目信息
        
        Args:
            platform: 平台代码
            parent_category: 父类目，None表示获取顶级类目
            
        Returns:
            完整的类目树
        """
        # 添加请求间隔
        time.sleep(self.request_interval)
        
        if parent_category is None:
            # 获取顶级类目
            categories = self.get_top_categories(platform)
        else:
            # 获取子类目
            logger.info(f"获取类目 '{parent_category.name}' 的子类目...")
            categories = self.get_category_tree(platform, parent_category.id)
        
        # 递归获取每个类目的子类目
        for category in categories:
            if category.leaf == 0:  # 非叶子节点，继续递归
                children = self.fetch_categories_recursive(platform, category)
                category.children = children
                
        return categories
    
    def save_categories(self, platform: str, categories: List[CategoryInfo], filename: Optional[str] = None):
        """
        保存类目信息到文件
        
        Args:
            platform: 平台代码
            categories: 类目列表
            filename: 文件名，默认为 {platform}_categories.json
        """
        if filename is None:
            platform_name = self.PLATFORMS.get(platform, platform)
            filename = f"{platform_name}_categories.json"
        
        # 确保数据目录存在
        os.makedirs('data', exist_ok=True)
        filepath = os.path.join('data', filename)
        
        # 转换为字典格式
        data = {
            'platform': platform,
            'platform_name': self.PLATFORMS.get(platform, platform),
            'total_count': self._count_categories(categories),
            'categories': [self._category_to_dict(cat) for cat in categories]
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"类目信息已保存到: {filepath}")
            logger.info(f"总计类目数量: {data['total_count']}")
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
    
    def _category_to_dict(self, category: CategoryInfo) -> Dict:
        """将CategoryInfo转换为字典"""
        result = asdict(category)
        if category.children:
            result['children'] = [self._category_to_dict(child) for child in category.children]
        return result
    
    def _count_categories(self, categories: List[CategoryInfo]) -> int:
        """递归计算类目总数"""
        count = len(categories)
        for category in categories:
            if category.children:
                count += self._count_categories(category.children)
        return count
    
    def fetch_all_platforms(self):
        """获取所有支持平台的类目信息"""
        for platform_code, platform_name in self.PLATFORMS.items():
            logger.info(f"开始获取{platform_name}({platform_code})类目信息...")
            try:
                categories = self.fetch_categories_recursive(platform_code)
                if categories:
                    self.save_categories(platform_code, categories)
                    logger.info(f"{platform_name}类目信息获取完成")
                else:
                    logger.warning(f"{platform_name}未获取到类目信息")
            except Exception as e:
                logger.error(f"获取{platform_name}类目信息失败: {e}")
            
            # 平台间添加较长间隔
            time.sleep(2)

def main():
    """主函数"""
    try:
        from config import API_CONFIG
        BASE_URL = API_CONFIG['BASE_URL']
        AUTHORIZATION = API_CONFIG['AUTHORIZATION']
    except ImportError:
        logger.warning("未找到config.py文件，使用默认配置")
        # 配置信息 - 请根据实际情况修改
        BASE_URL = "https://your-api-domain.com"  # 请替换为实际的API域名
        AUTHORIZATION = "your-auth-token"  # 请替换为实际的授权token

    # 检查配置
    if BASE_URL == "https://your-api-domain.com" or AUTHORIZATION == "your-auth-token":
        logger.error("请先配置正确的BASE_URL和AUTHORIZATION")
        logger.error("请修改config.py文件或直接修改main()函数中的配置")
        return

    # 创建获取器实例
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION)

    # 获取所有平台类目信息
    fetcher.fetch_all_platforms()

    logger.info("所有平台类目信息获取完成！")

if __name__ == "__main__":
    main()
