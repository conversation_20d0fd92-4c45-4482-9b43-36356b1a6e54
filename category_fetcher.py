#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聚水潭平台类目信息获取脚本
支持递归获取抖店、小红书、快手三个平台的类目信息
"""

import requests
import json
import time
import os
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('category_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class CategoryInfo:
    """类目信息数据类"""
    id: str
    categoryId: str
    categoryName: str
    platform: str
    pid: str = "0"
    level: str = "1"
    isParent: str = "N"
    longCategoryName: str = ""
    longCode: str = ""
    sorted: int = 1
    children: List['CategoryInfo'] = None

    def __post_init__(self):
        if self.children is None:
            self.children = []

class CategoryFetcher:
    """聚水潭类目信息获取器"""

    # 平台映射
    PLATFORMS = {
        'Fxg': '抖店',
        'Xiaohs': '小红书',
        'Kwaishop': '快手'
    }

    def __init__(self, base_url: str = "", authorization: str = "", shop_id: str = ""):
        """
        初始化类目获取器

        Args:
            base_url: API基础URL
            authorization: 授权token
            shop_id: 店铺ID
        """
        self.base_url = base_url.rstrip('/')
        self.shop_id = shop_id
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'app-version': 'GOODS_20250731210346',
            'appcode': 'goods.scm121.com',
            'authorization': authorization,
            'container': 'erp',
            'source': 'SUPPLIER',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Host': 'goods.scm121.com',
            'Connection': 'keep-alive'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 请求间隔（秒）
        self.request_interval = 0.5
        
    def get_categories_by_pid(self, shop_type: str, pid: str = "0") -> List[CategoryInfo]:
        """
        根据父类目ID获取类目列表

        Args:
            shop_type: 平台类型 (Fxg/Xiaohs/Kwaishop)
            pid: 父类目ID，0表示顶级类目

        Returns:
            类目列表
        """
        url = f"{self.base_url}/api/goods/platform/category/queryCategoryTreeByPid"
        params = {
            'shopType': shop_type,
            'pid': pid,
            'shopId': self.shop_id
        }

        try:
            logger.info(f"获取{self.PLATFORMS.get(shop_type, shop_type)}类目 (pid={pid})...")
            response = self.session.get(url, params=params)
            response.raise_for_status()

            data = response.json()
            if not data.get('success', False):
                logger.error(f"API返回错误: {data}")
                return []

            categories = []
            for item in data.get('data', []):
                category = CategoryInfo(
                    id=item.get('id', ''),
                    categoryId=item.get('categoryId', ''),
                    categoryName=item.get('categoryName', ''),
                    platform=shop_type,
                    pid=item.get('pid', '0'),
                    level=item.get('level', '1'),
                    isParent=item.get('isParent', 'N'),
                    longCategoryName=item.get('longCategoryName', ''),
                    longCode=item.get('longCode', ''),
                    sorted=item.get('sorted', 1)
                )
                categories.append(category)

            logger.info(f"获取到{len(categories)}个{self.PLATFORMS.get(shop_type, shop_type)}类目")
            return categories

        except requests.RequestException as e:
            logger.error(f"请求失败: {e}")
            return []
        except Exception as e:
            logger.error(f"处理响应失败: {e}")
            return []
    
    def fetch_categories_recursive(self, shop_type: str, parent_category: Optional[CategoryInfo] = None) -> List[CategoryInfo]:
        """
        递归获取类目信息

        Args:
            shop_type: 平台类型
            parent_category: 父类目，None表示获取顶级类目

        Returns:
            完整的类目树
        """
        # 添加请求间隔
        time.sleep(self.request_interval)

        if parent_category is None:
            # 获取顶级类目
            categories = self.get_categories_by_pid(shop_type, "0")
        else:
            # 获取子类目
            logger.info(f"获取类目 '{parent_category.categoryName}' 的子类目...")
            categories = self.get_categories_by_pid(shop_type, parent_category.categoryId)

        # 递归获取每个类目的子类目
        for category in categories:
            if category.isParent == "Y":  # 有子类目，继续递归
                children = self.fetch_categories_recursive(shop_type, category)
                category.children = children

        return categories
    

    
    def save_categories(self, shop_type: str, categories: List[CategoryInfo], filename: Optional[str] = None):
        """
        保存类目信息到文件

        Args:
            shop_type: 平台类型
            categories: 类目列表
            filename: 文件名，默认为 {platform}_categories.json
        """
        if filename is None:
            platform_name = self.PLATFORMS.get(shop_type, shop_type)
            filename = f"{platform_name}_categories.json"

        # 确保数据目录存在
        os.makedirs('data', exist_ok=True)
        filepath = os.path.join('data', filename)

        # 转换为字典格式
        data = {
            'shop_type': shop_type,
            'platform_name': self.PLATFORMS.get(shop_type, shop_type),
            'shop_id': self.shop_id,
            'total_count': self._count_categories(categories),
            'categories': [self._category_to_dict(cat) for cat in categories]
        }

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"类目信息已保存到: {filepath}")
            logger.info(f"总计类目数量: {data['total_count']}")
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
    
    def _category_to_dict(self, category: CategoryInfo) -> Dict:
        """将CategoryInfo转换为字典"""
        result = asdict(category)
        if category.children:
            result['children'] = [self._category_to_dict(child) for child in category.children]
        return result
    
    def _count_categories(self, categories: List[CategoryInfo]) -> int:
        """递归计算类目总数"""
        count = len(categories)
        for category in categories:
            if category.children:
                count += self._count_categories(category.children)
        return count
    
    def fetch_all_platforms(self):
        """获取所有支持平台的类目信息"""
        for shop_type, platform_name in self.PLATFORMS.items():
            logger.info(f"开始获取{platform_name}({shop_type})类目信息...")
            try:
                categories = self.fetch_categories_recursive(shop_type)
                if categories:
                    self.save_categories(shop_type, categories)
                    logger.info(f"{platform_name}类目信息获取完成")
                else:
                    logger.warning(f"{platform_name}未获取到类目信息")
            except Exception as e:
                logger.error(f"获取{platform_name}类目信息失败: {e}")

            # 平台间添加较长间隔
            time.sleep(2)

def main():
    """主函数"""
    try:
        from config import API_CONFIG
        BASE_URL = API_CONFIG['BASE_URL']
        AUTHORIZATION = API_CONFIG['AUTHORIZATION']
        SHOP_ID = API_CONFIG.get('SHOP_ID', '')
    except ImportError:
        logger.warning("未找到config.py文件，使用默认配置")
        # 配置信息 - 请根据实际情况修改
        BASE_URL = "https://goods.scm121.com"  # 聚水潭API域名
        AUTHORIZATION = "your-auth-token"  # 请替换为实际的授权token
        SHOP_ID = "your-shop-id"  # 请替换为实际的店铺ID

    # 检查配置
    if AUTHORIZATION == "your-auth-token" or SHOP_ID == "your-shop-id":
        logger.error("请先配置正确的AUTHORIZATION和SHOP_ID")
        logger.error("请修改config.py文件或直接修改main()函数中的配置")
        return

    # 创建获取器实例
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)

    # 获取所有平台类目信息
    fetcher.fetch_all_platforms()

    logger.info("所有平台类目信息获取完成！")

if __name__ == "__main__":
    main()
