#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例脚本
演示如何使用CategoryFetcher获取特定平台的类目信息
"""

from category_fetcher import CategoryFetcher
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_single_platform():
    """示例：获取单个平台的类目信息"""
    
    # 配置信息
    BASE_URL = "https://your-api-domain.com"
    AUTHORIZATION = "your-auth-token"
    
    # 创建获取器
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION)
    
    # 只获取抖店类目
    logger.info("开始获取抖店类目信息...")
    categories = fetcher.fetch_categories_recursive('FXG')
    
    if categories:
        fetcher.save_categories('FXG', categories)
        logger.info("抖店类目信息获取完成")
    else:
        logger.error("未获取到抖店类目信息")

def example_custom_platforms():
    """示例：获取自定义平台列表的类目信息"""
    
    # 配置信息
    BASE_URL = "https://your-api-domain.com"
    AUTHORIZATION = "your-auth-token"
    
    # 创建获取器
    fetcher = CategoryFetcher(BASE_URL, AUTHORIZATION)
    
    # 自定义平台列表
    custom_platforms = ['XHS', 'KWAISHOP']  # 只获取小红书和快手
    
    for platform in custom_platforms:
        platform_name = fetcher.PLATFORMS.get(platform, platform)
        logger.info(f"开始获取{platform_name}类目信息...")
        
        try:
            categories = fetcher.fetch_categories_recursive(platform)
            if categories:
                fetcher.save_categories(platform, categories)
                logger.info(f"{platform_name}类目信息获取完成")
            else:
                logger.warning(f"{platform_name}未获取到类目信息")
        except Exception as e:
            logger.error(f"获取{platform_name}类目信息失败: {e}")

def example_with_config():
    """示例：使用配置文件"""
    
    try:
        from config import API_CONFIG, PLATFORM_CONFIG
        
        # 使用配置文件中的设置
        fetcher = CategoryFetcher(
            API_CONFIG['BASE_URL'], 
            API_CONFIG['AUTHORIZATION']
        )
        
        # 设置请求间隔
        fetcher.request_interval = API_CONFIG['REQUEST_INTERVAL']
        
        # 获取所有配置的平台
        for platform_code in PLATFORM_CONFIG.keys():
            platform_info = PLATFORM_CONFIG[platform_code]
            logger.info(f"开始获取{platform_info['name']}类目信息...")
            
            try:
                categories = fetcher.fetch_categories_recursive(platform_code)
                if categories:
                    fetcher.save_categories(platform_code, categories)
                    logger.info(f"{platform_info['name']}类目信息获取完成")
            except Exception as e:
                logger.error(f"获取{platform_info['name']}类目信息失败: {e}")
                
    except ImportError:
        logger.error("请先创建config.py配置文件")

def example_analyze_categories():
    """示例：分析已保存的类目数据"""
    import json
    import os
    
    data_dir = 'data'
    if not os.path.exists(data_dir):
        logger.error("数据目录不存在，请先运行获取脚本")
        return
    
    for filename in os.listdir(data_dir):
        if filename.endswith('_categories.json'):
            filepath = os.path.join(data_dir, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                platform_name = data.get('platform_name', '未知平台')
                total_count = data.get('total_count', 0)
                categories = data.get('categories', [])
                
                logger.info(f"=== {platform_name} 类目分析 ===")
                logger.info(f"总类目数量: {total_count}")
                logger.info(f"顶级类目数量: {len(categories)}")
                
                # 分析层级分布
                level_counts = {}
                def count_levels(cats, current_level=1):
                    level_counts[current_level] = level_counts.get(current_level, 0) + len(cats)
                    for cat in cats:
                        if cat.get('children'):
                            count_levels(cat['children'], current_level + 1)
                
                count_levels(categories)
                
                for level, count in sorted(level_counts.items()):
                    logger.info(f"第{level}级类目: {count}个")
                
                logger.info("")
                
            except Exception as e:
                logger.error(f"分析文件{filename}失败: {e}")

if __name__ == "__main__":
    print("请选择要运行的示例:")
    print("1. 获取单个平台类目信息")
    print("2. 获取自定义平台列表类目信息")
    print("3. 使用配置文件获取类目信息")
    print("4. 分析已保存的类目数据")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        example_single_platform()
    elif choice == "2":
        example_custom_platforms()
    elif choice == "3":
        example_with_config()
    elif choice == "4":
        example_analyze_categories()
    else:
        print("无效选择")
