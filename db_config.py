#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置文件
"""

# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'your_username',   # 数据库用户名
    'password': 'your_password', # 数据库密码
    'database': 'your_database'  # 数据库名称
}

# 平台代码映射 (JSON平台代码 -> 数据库平台代码)
PLATFORM_MAPPING = {
    'TouTiaoFXG': 'FXG',      # 抖店
    'Xiaohs': 'XHS',          # 小红书
    'KWaiShop': 'KWAISHOP',   # 快手
    'WXChannel': 'SPHXD'      # 视频号小店
}

# 平台名称映射
PLATFORM_NAMES = {
    'TouTiaoFXG': '抖店',
    'Xiaohs': '小红书',
    'KWaiShop': '快手',
    'WXChannel': '视频号小店'
}

# 数据库表配置
TABLE_CONFIG = {
    'table_name': 'yt_category',
    'primary_keys': ['id', 'platform'],
    'fields': {
        'id': 'varchar(64)',           # 类目ID
        'platform': 'varchar(16)',     # 平台代码
        'name': 'varchar(64)',         # 类目名称
        'parent_id': 'varchar(64)',    # 父类目ID
        'level': 'tinyint',            # 类目等级
        'leaf': 'tinyint',             # 是否叶子节点
        'image_url': 'varchar(255)',   # 封面图URL
        'show_status': 'tinyint',      # 是否显示
        'delete_flag': 'tinyint',      # 是否已删除
        'creator_id': 'bigint',        # 创建人ID
        'creator_name': 'varchar(255)', # 创建人名字
        'modifier_id': 'bigint',       # 修改人ID
        'modifier_name': 'varchar(255)', # 修改人名字
        'created': 'datetime',         # 创建时间
        'updated': 'datetime'          # 更新时间
    }
}

# 同步配置
SYNC_CONFIG = {
    'batch_size': 1000,        # 批量插入大小
    'commit_interval': 100,    # 提交间隔
    'retry_count': 3,          # 重试次数
    'retry_delay': 1,          # 重试延迟(秒)
    'log_level': 'INFO',       # 日志级别
    'backup_before_sync': True # 同步前是否备份
}
