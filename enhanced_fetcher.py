#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版类目获取工具
提供更详细的日志、错误处理和验证功能
"""

import time
import requests
from category_fetcher import CategoryFetcher, CategoryInfo
from typing import List, Set, Dict, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedCategoryFetcher(CategoryFetcher):
    """增强版类目获取器"""
    
    def __init__(self, base_url: str, authorization: str, shop_id: str = ""):
        super().__init__(base_url, authorization, shop_id)
        self.fetched_ids: Set[str] = set()  # 记录已获取的类目ID
        self.failed_ids: Set[str] = set()   # 记录获取失败的类目ID
        self.retry_count = 3                # 重试次数
        self.detailed_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'retried_requests': 0
        }
    
    def get_categories_by_pid_with_retry(self, shop_type: str, pid: str = "0") -> List[CategoryInfo]:
        """
        带重试机制的类目获取
        
        Args:
            shop_type: 平台类型
            pid: 父类目ID
            
        Returns:
            类目列表
        """
        self.detailed_stats['total_requests'] += 1
        
        for attempt in range(self.retry_count):
            try:
                if attempt > 0:
                    self.detailed_stats['retried_requests'] += 1
                    logger.info(f"重试第 {attempt} 次获取类目 (pid={pid})...")
                    time.sleep(1)  # 重试前等待
                
                categories = self.get_categories_by_pid(shop_type, pid)
                
                if categories:
                    self.detailed_stats['successful_requests'] += 1
                    # 记录已获取的类目ID
                    for cat in categories:
                        self.fetched_ids.add(cat.categoryId)
                    return categories
                else:
                    logger.warning(f"第 {attempt + 1} 次尝试获取类目 (pid={pid}) 返回空结果")
                    
            except Exception as e:
                logger.error(f"第 {attempt + 1} 次尝试获取类目 (pid={pid}) 失败: {e}")
                if attempt == self.retry_count - 1:
                    self.failed_ids.add(pid)
                    self.detailed_stats['failed_requests'] += 1
        
        return []
    
    def fetch_categories_with_validation(self, shop_type: str, parent_category: Optional[CategoryInfo] = None, depth: int = 0) -> List[CategoryInfo]:
        """
        带验证的递归获取类目信息
        
        Args:
            shop_type: 平台类型
            parent_category: 父类目
            depth: 当前深度
            
        Returns:
            完整的类目树
        """
        # 控制递归深度，防止无限递归
        if depth > 10:
            logger.warning(f"递归深度超过限制 (depth={depth})，停止递归")
            return []
        
        # 添加请求间隔
        time.sleep(self.request_interval)
        
        if parent_category is None:
            # 获取顶级类目
            logger.info(f"获取{self.PLATFORMS.get(shop_type, shop_type)}顶级类目...")
            categories = self.get_categories_by_pid_with_retry(shop_type, "0")
            logger.info(f"获取到 {len(categories)} 个顶级类目")
        else:
            # 检查是否已经获取过这个类目的子类目
            if parent_category.categoryId in self.fetched_ids:
                logger.debug(f"类目 {parent_category.categoryId} 已经获取过，跳过")
                return []
            
            # 获取子类目
            indent = "  " * depth
            logger.info(f"{indent}获取类目 '{parent_category.categoryName}' (ID: {parent_category.categoryId}) 的子类目...")
            categories = self.get_categories_by_pid_with_retry(shop_type, parent_category.categoryId)
            
            if categories:
                logger.info(f"{indent}获取到 {len(categories)} 个子类目")
            else:
                logger.warning(f"{indent}类目 '{parent_category.categoryName}' 没有子类目或获取失败")
        
        # 递归获取每个类目的子类目
        for i, category in enumerate(categories):
            if category.isParent == "Y":  # 有子类目，继续递归
                indent = "  " * depth
                logger.info(f"{indent}处理第 {i+1}/{len(categories)} 个父类目: {category.categoryName}")
                
                try:
                    children = self.fetch_categories_with_validation(shop_type, category, depth + 1)
                    category.children = children
                    if children:
                        logger.info(f"{indent}类目 '{category.categoryName}' 获取到 {len(children)} 个子类目")
                    else:
                        logger.warning(f"{indent}类目 '{category.categoryName}' 标记为父类目但没有获取到子类目")
                except Exception as e:
                    logger.error(f"{indent}获取类目 '{category.categoryName}' 的子类目失败: {e}")
                    category.children = []
            else:
                # 叶子类目
                indent = "  " * depth
                logger.debug(f"{indent}叶子类目: {category.categoryName}")
        
        return categories
    
    def validate_completeness(self, categories: List[CategoryInfo]) -> Dict[str, any]:
        """
        验证类目获取的完整性
        
        Args:
            categories: 获取到的类目列表
            
        Returns:
            验证结果
        """
        validation_result = {
            'total_categories': self._count_categories(categories),
            'parent_categories': 0,
            'leaf_categories': 0,
            'empty_parent_categories': [],
            'max_depth': 0,
            'depth_distribution': {},
            'potential_issues': []
        }
        
        def validate_recursive(cats: List[CategoryInfo], depth: int = 0):
            validation_result['max_depth'] = max(validation_result['max_depth'], depth)
            
            if depth not in validation_result['depth_distribution']:
                validation_result['depth_distribution'][depth] = 0
            validation_result['depth_distribution'][depth] += len(cats)
            
            for cat in cats:
                if cat.isParent == "Y":
                    validation_result['parent_categories'] += 1
                    if not cat.children:
                        validation_result['empty_parent_categories'].append({
                            'id': cat.categoryId,
                            'name': cat.categoryName,
                            'depth': depth
                        })
                        validation_result['potential_issues'].append(
                            f"类目 '{cat.categoryName}' (ID: {cat.categoryId}) 标记为父类目但没有子类目"
                        )
                    else:
                        validate_recursive(cat.children, depth + 1)
                else:
                    validation_result['leaf_categories'] += 1
        
        validate_recursive(categories)
        return validation_result
    
    def fetch_platform_with_analysis(self, platform_code: str) -> Dict[str, any]:
        """
        获取平台类目并进行分析
        
        Args:
            platform_code: 平台代码
            
        Returns:
            获取结果和分析报告
        """
        platform_name = self.PLATFORMS.get(platform_code, platform_code)
        logger.info(f"开始获取{platform_name}({platform_code})类目信息...")
        
        # 重置统计信息
        self.fetched_ids.clear()
        self.failed_ids.clear()
        self.detailed_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'retried_requests': 0
        }
        
        start_time = time.time()
        
        try:
            # 获取类目
            categories = self.fetch_categories_with_validation(platform_code)
            
            # 验证完整性
            validation = self.validate_completeness(categories)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 生成报告
            report = {
                'platform_code': platform_code,
                'platform_name': platform_name,
                'success': True,
                'duration_seconds': round(duration, 2),
                'categories': categories,
                'statistics': {
                    'total_categories': validation['total_categories'],
                    'parent_categories': validation['parent_categories'],
                    'leaf_categories': validation['leaf_categories'],
                    'max_depth': validation['max_depth'],
                    'depth_distribution': validation['depth_distribution']
                },
                'request_stats': self.detailed_stats.copy(),
                'validation': validation,
                'fetched_category_ids': len(self.fetched_ids),
                'failed_category_ids': len(self.failed_ids)
            }
            
            # 保存数据
            if categories:
                self.save_categories(platform_code, categories)
                logger.info(f"✅ {platform_name}类目信息获取完成！")
                logger.info(f"总类目数量: {validation['total_categories']}")
                logger.info(f"获取耗时: {duration:.2f} 秒")
                logger.info(f"请求统计: 总计 {self.detailed_stats['total_requests']}, 成功 {self.detailed_stats['successful_requests']}, 失败 {self.detailed_stats['failed_requests']}")
                
                if validation['potential_issues']:
                    logger.warning(f"发现 {len(validation['potential_issues'])} 个潜在问题")
                    for issue in validation['potential_issues'][:5]:  # 只显示前5个
                        logger.warning(f"  - {issue}")
            else:
                logger.error(f"❌ 未获取到{platform_name}类目信息")
                report['success'] = False
            
            return report
            
        except Exception as e:
            logger.error(f"❌ 获取{platform_name}类目信息失败: {e}")
            return {
                'platform_code': platform_code,
                'platform_name': platform_name,
                'success': False,
                'error': str(e),
                'duration_seconds': time.time() - start_time
            }

def main():
    """主函数"""
    try:
        from config import API_CONFIG
        BASE_URL = API_CONFIG['BASE_URL']
        AUTHORIZATION = API_CONFIG['AUTHORIZATION']
        SHOP_ID = API_CONFIG.get('SHOP_ID', '')
    except ImportError:
        logger.error("请先配置 config.py 文件")
        return
    
    # 创建增强版获取器
    fetcher = EnhancedCategoryFetcher(BASE_URL, AUTHORIZATION, SHOP_ID)
    
    # 显示平台选择菜单
    print("\n增强版聚水潭类目获取工具")
    print("=" * 50)
    print("支持的平台:")
    platforms = list(fetcher.PLATFORMS.items())
    for i, (code, name) in enumerate(platforms, 1):
        need_shop_id = "需要shopId" if code in fetcher.PLATFORMS_NEED_SHOP_ID else "无需shopId"
        print(f"{i}. {name} ({code}) - {need_shop_id}")
    
    print("0. 退出")
    
    while True:
        try:
            choice = input(f"\n请选择要获取的平台 (0-{len(platforms)}): ").strip()
            
            if choice == "0":
                print("退出程序")
                break
            elif choice.isdigit() and 1 <= int(choice) <= len(platforms):
                platform_code = platforms[int(choice) - 1][0]
                
                # 执行获取和分析
                report = fetcher.fetch_platform_with_analysis(platform_code)
                
                # 显示详细报告
                print(f"\n{'='*60}")
                print(f"{report['platform_name']} 获取报告")
                print(f"{'='*60}")
                
                if report['success']:
                    stats = report['statistics']
                    print(f"✅ 获取成功")
                    print(f"总类目数量: {stats['total_categories']}")
                    print(f"父类目: {stats['parent_categories']}, 叶子类目: {stats['leaf_categories']}")
                    print(f"最大深度: {stats['max_depth']}")
                    print(f"获取耗时: {report['duration_seconds']} 秒")
                    
                    req_stats = report['request_stats']
                    print(f"请求统计: 总计 {req_stats['total_requests']}, 成功 {req_stats['successful_requests']}, 失败 {req_stats['failed_requests']}")
                    
                    if report['validation']['potential_issues']:
                        print(f"⚠️ 发现 {len(report['validation']['potential_issues'])} 个潜在问题")
                else:
                    print(f"❌ 获取失败: {report.get('error', '未知错误')}")
                
                break
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            break
        except Exception as e:
            logger.error(f"操作失败: {e}")
            break

if __name__ == "__main__":
    main()
